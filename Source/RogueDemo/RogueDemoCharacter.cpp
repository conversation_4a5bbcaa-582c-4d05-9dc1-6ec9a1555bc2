// Copyright Epic Games, Inc. All Rights Reserved.

#include "RogueDemoCharacter.h"
#include "Engine/LocalPlayer.h"
#include "Camera/CameraComponent.h"
#include "Components/CapsuleComponent.h"
#include "GameFramework/CharacterMovementComponent.h"
#include "GameFramework/SpringArmComponent.h"
#include "GameFramework/Controller.h"
#include "EnhancedInputComponent.h"
#include "EnhancedInputSubsystems.h"
#include "InputActionValue.h"
#include "AbilitySystem/RogueAbilitySystemComponent.h"
#include "Game/RogueGameplayTags.h"
#include "Input/RogueInputComponent.h"

DEFINE_LOG_CATEGORY(LogTemplateCharacter);

ARogueDemoCharacter::ARogueDemoCharacter()
{
	// Set size for collision capsule
	// GetCapsuleComponent()->InitCapsuleSize(42.f, 96.0f);

	// Don't rotate when the controller rotates. Let that just affect the camera.
	bUseControllerRotationPitch = false;
	bUseControllerRotationYaw = false;
	bUseControllerRotationRoll = false;

	// Configure character movement
	GetCharacterMovement()->bOrientRotationToMovement = true;
	GetCharacterMovement()->RotationRate = FRotator(0.0f, 500.0f, 0.0f);

	// Note: For faster iteration times these variables, and many more, can be tweaked in the Character Blueprint
	// instead of recompiling to adjust them
	GetCharacterMovement()->JumpZVelocity = 500.f;
	GetCharacterMovement()->AirControl = 0.35f;
	GetCharacterMovement()->MaxWalkSpeed = 500.f;
	GetCharacterMovement()->MinAnalogWalkSpeed = 20.f;
	GetCharacterMovement()->BrakingDecelerationWalking = 2000.f;
	GetCharacterMovement()->BrakingDecelerationFalling = 1500.0f;

	// Create a camera boom (pulls in towards the player if there is a collision)
	CameraBoom = CreateDefaultSubobject<USpringArmComponent>(TEXT("CameraBoom"));
	CameraBoom->SetupAttachment(RootComponent);
	CameraBoom->TargetArmLength = 400.0f;
	CameraBoom->bUsePawnControlRotation = true;

	// Create a follow camera
	FollowCamera = CreateDefaultSubobject<UCameraComponent>(TEXT("FollowCamera"));
	FollowCamera->SetupAttachment(CameraBoom, USpringArmComponent::SocketName);
	FollowCamera->bUsePawnControlRotation = false;

	// Note: The skeletal mesh and anim blueprint references on the Mesh component (inherited from Character) 
	// are set in the derived blueprint asset named ThirdPersonCharacter (to avoid direct content references in C++)
}

void ARogueDemoCharacter::SetupPlayerInputComponent(UInputComponent* PlayerInputComponent)
{
	// Set up action bindings
	if (URogueInputComponent* RogueInputComponent = Cast<URogueInputComponent>(PlayerInputComponent))
	{
		/* ~AbilityInputs */
		TArray<uint32> AbilityInputHandles;
		RogueInputComponent->BindAbilityActions(RogueInputConfig, this, &ARogueDemoCharacter::AbilityInputTagPressed,
		                                        &ARogueDemoCharacter::AbilityInputTagHeld,
		                                        &ARogueDemoCharacter::AbilityInputTagReleased, AbilityInputHandles);
		/* end ~AbilityInputs */

		/* ~NativeInputs */
		// Jumping
		RogueInputComponent->BindNativeAction(RogueInputConfig, RogueGameplayTags::InputTag_Jump,
		                                      ETriggerEvent::Started, this, &ARogueDemoCharacter::DoJumpStart);
		RogueInputComponent->BindNativeAction(RogueInputConfig, RogueGameplayTags::InputTag_Jump,
		                                      ETriggerEvent::Completed, this, &ARogueDemoCharacter::DoJumpEnd);
		// Moving
		RogueInputComponent->BindNativeAction(RogueInputConfig, RogueGameplayTags::InputTag_Move,
		                                      ETriggerEvent::Triggered, this, &ARogueDemoCharacter::Move);
		// Looking
		RogueInputComponent->BindNativeAction(RogueInputConfig, RogueGameplayTags::InputTag_Look_Mouse,
		                                      ETriggerEvent::Triggered, this, &ARogueDemoCharacter::Look);
		RogueInputComponent->BindNativeAction(RogueInputConfig, RogueGameplayTags::InputTag_Look_Gamepad,
		                                      ETriggerEvent::Triggered, this, &ARogueDemoCharacter::Look);
		RogueInputComponent->BindNativeAction(RogueInputConfig, RogueGameplayTags::InputTag_Sprint,
		                                      ETriggerEvent::Started, this, &ARogueDemoCharacter::Sprint);
		/* end ~NativeInputs */
	}
	else
	{
		UE_LOG(LogTemplateCharacter, Error,
		       TEXT(
			       "'%s' Failed to find an Enhanced Input component! This template is built to use the Enhanced Input system. If you intend to use the legacy system, then you will need to update this C++ file."
		       ), *GetNameSafe(this));
	}
}

void ARogueDemoCharacter::Move(const FInputActionValue& Value)
{
	// input is a Vector2D
	FVector2D MovementVector = Value.Get<FVector2D>();

	// route the input
	DoMove(MovementVector.X, MovementVector.Y);
}

void ARogueDemoCharacter::Look(const FInputActionValue& Value)
{
	// input is a Vector2D
	FVector2D LookAxisVector = Value.Get<FVector2D>();

	// route the input
	DoLook(LookAxisVector.X, LookAxisVector.Y);
}

void ARogueDemoCharacter::Sprint(const FInputActionValue& Value)
{
	bSprinting = !bSprinting;

	if (bSprinting)
	{
		GetRogueASC()->AddLooseGameplayTag(RogueGameplayTags::StateTag_Movement_Sprinting);
	}else
	{
		GetRogueASC()->RemoveLooseGameplayTag(RogueGameplayTags::StateTag_Movement_Sprinting);
	}

	Server_Sprint(bSprinting);
}

void ARogueDemoCharacter::DoMove(float Right, float Forward)
{
	if (GetController() != nullptr)
	{
		// find out which way is forward
		const FRotator Rotation = GetController()->GetControlRotation();
		const FRotator YawRotation(0, Rotation.Yaw, 0);

		// get forward vector
		const FVector ForwardDirection = FRotationMatrix(YawRotation).GetUnitAxis(EAxis::X);

		// get right vector 
		const FVector RightDirection = FRotationMatrix(YawRotation).GetUnitAxis(EAxis::Y);

		// add movement 
		AddMovementInput(ForwardDirection, Forward);
		AddMovementInput(RightDirection, Right);
	}
}

void ARogueDemoCharacter::DoLook(float Yaw, float Pitch)
{
	if (GetController() != nullptr)
	{
		// add yaw and pitch input to controller
		AddControllerYawInput(Yaw);
		AddControllerPitchInput(Pitch);
	}
}

void ARogueDemoCharacter::DoJumpStart()
{
	// signal the character to jump
	Jump();
}

void ARogueDemoCharacter::DoJumpEnd()
{
	// signal the character to stop jumping
	StopJumping();
}

void ARogueDemoCharacter::AbilityInputTagPressed(FGameplayTag InputTag)
{
	GetRogueASC()->RogueAbilityInputTagPressed(InputTag);
}

void ARogueDemoCharacter::AbilityInputTagHeld(FGameplayTag InputTag)
{
	GetRogueASC()->RogueAbilityInputTagHeld(InputTag);
}

void ARogueDemoCharacter::AbilityInputTagReleased(FGameplayTag InputTag)
{
	GetRogueASC()->RogueAbilityInputTagReleased(InputTag);
}

void ARogueDemoCharacter::Server_Sprint_Implementation(bool InbSprinting)
{
	bSprinting = InbSprinting;

	if (bSprinting)
	{
		GetRogueASC()->AddLooseGameplayTag(RogueGameplayTags::StateTag_Movement_Sprinting);
	}else
	{
		GetRogueASC()->RemoveLooseGameplayTag(RogueGameplayTags::StateTag_Movement_Sprinting);
	}
}

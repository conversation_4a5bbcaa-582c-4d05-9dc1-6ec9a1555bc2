<wpf:ResourceDictionary xml:space="preserve" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:s="clr-namespace:System;assembly=mscorlib" xmlns:ss="urn:shemas-jetbrains-com:settings-storage-xaml" xmlns:wpf="http://schemas.microsoft.com/winfx/2006/xaml/presentation">
	<s:String x:Key="/Default/CodeInspection/ExcludedFiles/FilesAndFoldersToSkip2/=7020124F_002D9FFC_002D4AC3_002D8F3D_002DAAB8E0240759_002Ff_003ANubisah_002Easm_002Fl_003AC_0021_003FUsers_003F62750_003FAppData_003FLocal_003FTemp_003FSandboxFiles_003FGiwuliv_003FNubisah_002Easm/@EntryIndexedValue">ForceIncluded</s:String>
	<s:String x:Key="/Default/CodeInspection/ExcludedFiles/FilesAndFoldersToSkip2/=7020124F_002D9FFC_002D4AC3_002D8F3D_002DAAB8E0240759_002Ff_003APycicyn_002Easm_002Fl_003AC_0021_003FUsers_003F62750_003FAppData_003FLocal_003FTemp_003FSandboxFiles_003FDomakib_003FPycicyn_002Easm/@EntryIndexedValue">ForceIncluded</s:String>
	<s:String x:Key="/Default/CodeInspection/ExcludedFiles/FilesAndFoldersToSkip2/=93755BDC_002D36FB_002D3F38_002DAEC1_002D444AA2DA8D1C_002Fdl_003ASource_003A_002E_002E_003F_002E_002E_003F_002E_002E_003F_002E_002E_003FUnrealEngine_003FUE_005F5_002E6_003FEngine_003FSource_002Fd_003AThirdParty_002Fd_003Aheapprofd_002Ff_003Aheap_005Fprofile_002Eh/@EntryIndexedValue">ForceIncluded</s:String></wpf:ResourceDictionary>